<?php
session_start();

// Simple setMessage function implementation if not defined
if (!function_exists('setMessage')) {
    function setMessage($message, $type = 'info') {
        $_SESSION['message'] = $message;
        $_SESSION['message_type'] = $type;
    }
}

// Test different types of notifications
$test_type = isset($_GET['type']) ? $_GET['type'] : 'success';

switch($test_type) {
    case 'success':
        setMessage('Success - thank you for invest.', 'success');
        break;
    case 'error':
        setMessage('Insufficient funds to cover this investment.', 'error');
        break;
    case 'warning':
        setMessage('Invest min $100 and max $10000 multiple of 10.', 'warning');
        break;
    case 'invalid':
        setMessage('Invalid amount.', 'error');
        break;
    default:
        setMessage('Test notification - popup system is working!', 'info');
}

// Redirect to invest_now.php with a sample investment plan ID
header('Location: invest_now.php?i=1');
exit;
?>
