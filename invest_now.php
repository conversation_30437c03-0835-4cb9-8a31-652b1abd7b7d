<?php
session_start();
include_once '../lib/config.php';

// Check if user is logged in
if (!isset($_SESSION['userid']) || empty($_SESSION['userid'])) {
  header('Location: index.php');
  exit;
}

$uid = $_SESSION['userid'];
$user = get_user_details($uid);

// Debug: Check if user data is retrieved
if (!$user) {
  echo "Error: User data not found for UID: " . $uid;
  exit;
}

$reward_arr = get_reward();
$title = 'Investment Order';
$childs = get_single_dimensional(get_child_levels($uid));
$recid = isset($_GET['i']) ? (int) $_GET['i'] : 0;
$row = my_fetch_object(my_query("SELECT * FROM investments_plan WHERE recid='" . $recid . "'"));
if (!$row) {
  redirect('invest.php');
}
$min = $row->amount_from * 1;
$max = $row->amount_to * 1;
if (!$user->name || !$user->email || !$user->mobile || !$user->bitcoin || !$user->wallet_address) {
  //redirect('profile.php');die;
}

// Generate random ROI between 5% and 25% for display purposes
$roi = $row->percentage;
// Randomly decide if it's positive or negative trend
$trend = rand(0, 10) > 2 ? 'up' : 'down';
?>
<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <title><?php echo $title; ?> - <?php echo SITE_NAME; ?></title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <link rel="stylesheet" href="./assets/cy/libs/bootstrap/css/bootstrap.min.css?vs=100">
  <link rel="icon" type="image/png" href="./assets/fav.png" sizes="96x96" />
  <link rel="stylesheet" href="./assets/cy/libs/priority-navigation-master/dist/priority-nav-core.css?vs=100">
  <link rel="stylesheet" href="./assets/cy/libs/swiper/swiper.min.css?vs=100">
  <link rel="stylesheet" href="./assets/cy/libs/bootstrap-select-1.14.0/dist/css/bootstrap-select.min.css?vs=100">
  <link rel="stylesheet" href="./assets/cy/libs/plyr/dist/plyr.css?vs=100">
  <link rel="stylesheet" href="./assets/cy/libs/ion.rangeSlider-master/css/ion.rangeSlider.min.css?vs=100">
  <link rel="stylesheet" href="./assets/cy/libs/toastr-master/build/toastr.min.css?vs=100">
  <link rel="stylesheet" href="./assets/cy/css/main.css?vs=100">
  <link rel="stylesheet" href="./assets/cy/css/media.css?vs=100">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="shortcut icon" href="./assets/fav.png" />

  <style>
    .openUserInfo {
      cursor: pointer;
    }

    .sectionLotteriesItemsSMEmpty {
      min-height: 46px;
    }

    button.change-pswd-type-link {
      border: 0px;
    }

    .logo-img img {
      width: 150px;
    }

    .db-page--wide-menu .db-side-logo__text-block {
      width: 200px !important;
    }

    .db-side-logo__text {
      padding-left: 0 !important;
    }

    .mobile-panel-top-left .logo-wrapper {
      display: flex;
      max-width: 150px;
    }

    .db-side-logo__text .image {
      width: 150px;
      min-width: 61px;
      max-width: none;
    }

    .db-page-topline-panel__right__logo .logo-wrapper {
      max-width: 160px;
    }

    .topline-refill-btn {
      line-height: 1;
      color: #EBF4FF;
      font-size: 15px;
      text-align: center;
      height: 40px;
    }

    .topline-refill-btn {
      width: 170px;
    }

    .toplinen img {
      width: 17px;
    }

    @media only screen and (min-width: 601px) {
      .db-page-topline__right {
        margin-left: -200px;
      }
    }

    .dropdown-container {
      margin-top: 5px;
      border-radius: 5px;
      padding-bottom: 5px;
    }

    .dropdown-container {
      display: none;
      border-radius: 5px;
    }

    @media screen and (max-width: 780px) {
      .mobhidden {
        display: none !important;
      }

      .cabMenu li.hidden {
        display: flex !important;
      }

      .langList {
        display: none
      }
    }

    .dropdown-btn .fa {
      position: absolute;
      right: 5px;
      top: 65px;
      font-size: 20px;
      color: #fff;
    }

    .fa-caret-up {
      display: none;
    }

    .dactive .fa-caret-down {
      display: none;
    }

    .dactive .fa-caret-up {
      display: inline-block !important;
    }

    .dropdown-container li {
      height: auto;
      padding: 5px 10px 5px 10px;
      line-height: 1;
    }

    .dropdown-containers li:hover {
      background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
      ;
      color: #fff;
    }

    .dropdown-container li:hover cap {
      color: #fff;
    }

    .hidden:hover {
      background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    }

    .capa a {
      margin-left: 20px;
      color: #fff;
      text-decoration: none;
      font-size: 14px;
    }

    .db-sidemenu-icon img {
      width: 25px;
    }

    .db-sidemenu-icon {
      position: inherit;
    }

    .mobile-db-menu-link__text {
      margin-left: 5px;
    }

    /* Investment Order Styling */
    .investment-order-card {
      background: linear-gradient(135deg, #1c2127 0%, #121517 100%);
      border-radius: 12px;
      border: 1px solid rgba(255, 255, 255, 0.05);
      padding: 30px;
      margin-bottom: 30px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      position: relative;
      overflow: hidden;
    }

    .investment-order-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, #17c964, #0170ef, transparent);
    }

    .plan-info-header {
      text-align: center;
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    }

    .plan-info-title {
      background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      font-size: 1.8rem;
      font-weight: 600;
      margin-bottom: 10px;
    }

    .plan-info-details {
      color: #eaecef;
      font-size: 1.1rem;
      margin-bottom: 20px;
    }

    .plan-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 15px;
      margin-bottom: 25px;
    }

    .plan-stat-item {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 8px;
      padding: 15px;
      text-align: center;
    }

    .plan-stat-label {
      font-size: 12px;
      color: #848e9c;
      margin-bottom: 5px;
      text-transform: uppercase;
    }

    .plan-stat-value {
      font-size: 16px;
      font-weight: 600;
      color: #eaecef;
    }

    .form-group {
      margin-bottom: 25px;
    }

    .form-label {
      display: block;
      margin-bottom: 8px;
      color: #eaecef;
      font-weight: 500;
    }

    .form-control {
      width: 100%;
      padding: 12px 15px;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      color: #eaecef;
      font-size: 16px;
      transition: all 0.3s ease;
    }

    .form-control:focus {
      border-color: rgba(23, 201, 100, 0.5);
      box-shadow: 0 0 0 2px rgba(23, 201, 100, 0.2);
      outline: none;
    }

    .input-group {
      display: flex;
      align-items: center;
    }

    .input-group-addon {
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-left: none;
      padding: 12px 15px;
      border-radius: 0 8px 8px 0;
      color: #848e9c;
    }

    .range-slider {
      margin: 15px 0;
    }

    .range-slider input[type="range"] {
      width: 100%;
      height: 6px;
      border-radius: 3px;
      background: rgba(255, 255, 255, 0.1);
      outline: none;
      -webkit-appearance: none;
    }

    .range-slider input[type="range"]::-webkit-slider-thumb {
      -webkit-appearance: none;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
      cursor: pointer;
    }

    .range-labels {
      display: flex;
      justify-content: space-between;
      margin-top: 5px;
      font-size: 12px;
      color: #848e9c;
    }

    .invest-submit-btn {
      width: 100%;
      padding: 15px 25px;
      background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
      color: #fff;
      border: none;
      border-radius: 8px;
      font-weight: 600;
      font-size: 1.1rem;
      cursor: pointer;
      transition: all 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .invest-submit-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(23, 201, 100, 0.4);
      background: linear-gradient(114deg, #0170ef 0%, #17c964 100%);
    }

    .form-check {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
    }

    .form-check-input {
      margin-right: 10px;
    }

    .form-check-label {
      color: #eaecef;
      font-size: 14px;
    }

    .form-check-label a {
      color: #17c964;
      text-decoration: none;
    }

    .form-check-label a:hover {
      color: #0170ef;
    }
  </style>

</head>

<body class="page page--main">
  <div class="page-inner">
    <div class="page-progress-block xProgress d-none">
      <div class="page-progress">
        <div class="page-progress__progress xProgressPercent" style="width: 0%;"></div>
      </div>
    </div>

    </style>

    <div class="mobile-panel-block">
      <div class="mobile-panel-top-block">
        <div class="mobile-panel-top-left">
          <div class="logo-wrapper"> <a href="" class="logo">
              <div class="logo-img"> <img class="image" src="assets/logo.png" alt=""> </div>
            </a> </div>
        </div>
        <div class="mobile-panel-top-right">
          <div class="topline-lang-panel-block">
            <div class="topline-lang-panel"> <a href="javascript:void(0)" class="current-lang" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                <div class="current-lang__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                <div class="current-lang__text">EN</div>
              </a>
              <div class="topline-lang-dropdown dropdown-menu dropdown-menu-end"> <a href="account/?lang=default" class="lang-link">
                  <div class="lang-link__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                  <div class="lang-link__text">EN</div>
                </a> </div>
            </div>
          </div>
          <div class="mobile-panel-close-btn-block">
            <button type="button" class="mobile-panel-close-btn" onclick="closeSidebar()"></button>
          </div>
        </div>
      </div>
      <div class="mobile-db-menu-block">
        <div class="mobile-db-menu">
          <ul>
            <li class="mobile-db-menu-item mobile-db-menu-item--dashboard"> <a href='dashboard.php' class="mobile-db-menu-link mobile-db-menu-link--dashboard">
                <div class="mobile-db-menu-link__icon"></div>
                <div class="mobile-db-menu-link__text-block">
                  <div class="mobile-db-menu-link__text">Dashboard</div>
                </div>
              </a> </li>
            <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings ">
                <div class="mobile-db-menu-link__icon"></div>
                <div class="mobile-db-menu-link__text-block">
                  <div class="mobile-db-menu-link__text">Account <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
                </div>
              </a> </li>
            <div class="dropdown-container">
              <li class="hidden"><span class="capa"> <a href="profile.php" class="openMod" data-modal="buy">Profile</a> </span> </li>
              <li class="hidden"><span class="capa"><a href="change_password.php">Change Password</a></span> </li>
            </div>
            <li class="mobile-db-menu-item mobile-db-menu-item--refill dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--refill">
                <div class="mobile-db-menu-link__icon"></div>
                <div class="mobile-db-menu-link__text-block">
                  <div class="mobile-db-menu-link__text">Deposit <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
                </div>
              </a> </li>
            <div class="dropdown-container">
              <li class="hidden"><span class="capa"> <a href="deposit_block.php" class="openMod" data-modal="buy">Add Fund</a> </span> </li>
              <li class="hidden"><span class="capa"><a href="report_deposit_block.php">Report</a></span> </li>
            </div>
            <li class="mobile-db-menu-item mobile-db-menu-item--withdrawal dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--withdrawal active">
                <div class="mobile-db-menu-link__icon"></div>
                <div class="mobile-db-menu-link__text-block">
                  <div class="mobile-db-menu-link__text">Activation <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
                </div>
              </a> </li>
            <div class="dropdown-container">
              <li class="hidden"><span class="capa"> <a href="invest.php" class="openMod" data-modal="buy">Activate</a> </span> </li>
              <li class="hidden"><span class="capa"><a href="report_invest.php">Report</a></span> </li>
            </div>
            <li class="mobile-db-menu-item mobile-db-menu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--deposits ">
                <div class="db-sidemenu-icon"><img src="assets/network.png"></div>
                <div class="mobile-db-menu-link__text-block">
                  <div class="mobile-db-menu-link__text">Network <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
                </div>
              </a> </li>
            <div class="dropdown-container">
              <li class="hidden"><span class="capa"> <a href="direct_referral_left.php" class="openMod" data-modal="buy">Left Direct</a> </span> </li>
              <li class="hidden"><span class="capa"><a href="direct_referral.php">Right Direct</a></span> </li>
              <li class="hidden"><span class="capa"><a href="downline.php">Total Team</a></span> </li>
              <li class="hidden"><span class="capa"><a href="tree_view.php">Tree View</a></span> </li>
            </div>
            <li class="mobile-db-menu-item mobile-db-menu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--deposits ">
                <div class="mobile-db-menu-link__icon"></div>
                <div class="mobile-db-menu-link__text-block">
                  <div class="mobile-db-menu-link__text">Earning <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
                </div>
              </a> </li>
            <div class="dropdown-container">
              <!--<li class="hidden" ><span class="capa"> <a href="report_direct.php" class="openMod" data-modal="buy">Non-Working Bonus</a> </span> </li>-->
              <li class="hidden"><span class="capa"><a href="report_binary.php" class="openMod" data-modal="buy">Matching Bonus</a></span> </li>
              <li class="hidden"><span class="capa"><a href="report_royalty.php">Reward Bonus</a></span> </li>
              <li class="hidden"><span class="capa"><a href="report_growth.php">ROI Income</a></span> </li>
              <li class="hidden"><span class="capa"><a href="report_royalty.php?type=1">Lifetime Cash Back Income</a></span> </li>
              <!--<li class="hidden" ><span class="capa"><a href="report_all.php">All Reports</a></span> </li>-->
            </div>
            <li class="mobile-db-menu-item mobile-db-menu-item--affiliate dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--affiliate ">
                <div class="mobile-db-menu-link__icon"></div>
                <div class="mobile-db-menu-link__text-block">
                  <div class="mobile-db-menu-link__text">Wallet <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
                </div>
              </a> </li>
            <div class="dropdown-container">
              <li class="hidden"><span class="capa"><a href="fund_transfer.php?type=1">Fund Transfer</a></span> </li>
              <li class="hidden"><span class="capa"><a href="fund_transfer3.php?type=1">Self Transfer</a></span> </li>
              <li class="hidden"><span class="capa"><a href="report_fund_transfer.php">Transfer Report</a></span> </li>
              <li class="hidden"><span class="capa"><a href="profile.php">Wallet Details</a></span> </li>
              <!--<li class="hidden" ><span class="capa"><a href="account.php">Account Overview</a></span> </li>-->
            </div>
            <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings ">
                <div class="db-sidemenu-icon"><img src="assets/withdraw.png"></div>
                <div class="mobile-db-menu-link__text-block">
                  <div class="mobile-db-menu-link__text">Withdrawal <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
                </div>
              </a> </li>
            <div class="dropdown-container">
              <li class="hidden"><span class="capa"> <a href="withdrawal_block.php?type=10" class="openMod" data-modal="buy">Fund Withdrawal</a> </span> </li>
              <li class="hidden"><span class="capa"><a href="report_withdrawal_block.php">Report</a></span> </li>
            </div>
            <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="logout.php" class="mobile-db-menu-link mobile-db-menu-link--settings ">
                <div class="topline-logout-btn__icon"></div>
                <div class="mobile-db-menu-link__text-block">
                  <div class="mobile-db-menu-link__text">Logout</div>
                </div>
              </a> </li>
          </ul>
        </div>
      </div>
      <div class="mobile-panel-menu-block">
        <div class="mobile-panel-menu">
          <div class="mobile-menu">

          </div>
        </div>
      </div>
    </div>

    <div class="db-page-block">
      <div class="container">
        <div class="row">
          <div class="col-12">
            <div class="db-page db-page--wide-menu">
              <div class="db-page-left">
                <div class="db-side-block">
                  <div class="db-side-logo-block"> <a href="" class="db-side-logo">
                      <div class="db-side-logo__text-block">
                        <div class="db-side-logo__text"> <img class="image" src="assets/logo.png" alt=""> </div>
                      </div>
                    </a> </div>
                  <div class="db-side-toggle-panel-btn-block">
                    <button class="db-side-toggle-panel-btn">
                      <div class="db-side-toggle-panel-btn__text-block">
                        <div class="db-side-toggle-panel-btn__text">Navigation</div>
                      </div>
                      <div class="db-side-toggle-panel-btn__icon"></div>
                    </button>
                  </div>
                  <div class="db-sidemenu-block">
                    <div class="db-sidemenu">
                      <ul>
                        <li class="db-sidemenu-item db-sidemenu-item--dashboard"> <a href='dashboard.php' class="db-sidemenu-link db-sidemenu-link--dashboard">
                            <div class="db-sidemenu-link__icon"></div>
                            <div class="db-sidemenu-link__text-block">
                              <div class="db-sidemenu-link__text">Dashboard</div>
                            </div>
                          </a> </li>
                        <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
                            <div class="db-sidemenu-link__icon"></div>
                            <div class="db-sidemenu-link__text-block">
                              <div class="db-sidemenu-link__text">Account <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
                            </div>
                          </a> </li>
                        <div class="dropdown-container">
                          <li class="hidden"><span class="capa"> <a href="profile.php" class="openMod" data-modal="buy">Profile</a> </span> </li>
                          <li class="hidden"><span class="capa"><a href="change_password.php">Change Password</a></span> </li>
                        </div>

                        <li class="db-sidemenu-item db-sidemenu-item--refill dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--refill">
                            <div class="db-sidemenu-link__icon"></div>
                            <div class="db-sidemenu-link__text-block">
                              <div class="db-sidemenu-link__text">Deposit <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
                            </div>
                          </a> </li>
                        <div class="dropdown-container">
                          <li class="hidden"><span class="capa"> <a href="deposit_block.php" class="openMod" data-modal="buy">Add Fund</a> </span> </li>
                          <li class="hidden"><span class="capa"><a href="report_deposit_block.php">Report</a></span> </li>
                        </div>

                        <li class="db-sidemenu-item db-sidemenu-item--withdrawal dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--withdrawal active">
                            <div class="db-sidemenu-link__icon"></div>
                            <div class="db-sidemenu-link__text-block">
                              <div class="db-sidemenu-link__text">Activation <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
                            </div>
                          </a> </li>
                        <div class="dropdown-container">
                          <li class="hidden"><span class="capa"> <a href="invest.php" class="openMod" data-modal="buy">Activate</a> </span> </li>
                          <li class="hidden"><span class="capa"><a href="report_invest.php">Report</a></span> </li>
                        </div>

                        <li class="db-sidemenu-item db-sidemenu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--deposits ">
                            <div class="db-sidemenu-icon"><img src="assets/network.png"></div>
                            <div class="db-sidemenu-link__text-block">
                              <div class="db-sidemenu-link__text">Network <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
                            </div>
                          </a> </li>
                        <div class="dropdown-container">
                          <li class="hidden"><span class="capa"> <a href="direct_referral_left.php" class="openMod" data-modal="buy">Left Direct</a> </span> </li>
                          <li class="hidden"><span class="capa"><a href="direct_referral.php">Right Direct</a></span> </li>
                          <li class="hidden"><span class="capa"><a href="downline.php">Total Team</a></span> </li>
                          <li class="hidden"><span class="capa"><a href="tree_view.php">Tree View</a></span> </li>
                        </div>

                        <li class="db-sidemenu-item db-sidemenu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--deposits ">
                            <div class="db-sidemenu-link__icon"></div>
                            <div class="db-sidemenu-link__text-block">
                              <div class="db-sidemenu-link__text">Earning <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
                            </div>
                          </a> </li>
                        <div class="dropdown-container">
                          <!--<li class="hidden" ><span class="capa"> <a href="report_direct.php" class="openMod" data-modal="buy">Non-Working Bonus</a> </span> </li>-->
                          <li class="hidden"><span class="capa"><a href="report_binary.php" class="openMod" data-modal="buy">Matching Bonus</a></span> </li>
                          <li class="hidden"><span class="capa"><a href="report_royalty.php">Reward Bonus</a></span> </li>
                          <li class="hidden"><span class="capa"><a href="report_growth.php">ROI Income</a></span> </li>
                          <li class="hidden"><span class="capa"><a href="report_royalty.php?type=1">Lifetime Cash Back Income</a></span> </li>
                          <!--<li class="hidden" ><span class="capa"><a href="report_all.php">All Reports</a></span> </li>-->

                        </div>
                        <li class="db-sidemenu-item db-sidemenu-item--affiliate dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--affiliate ">
                            <div class="db-sidemenu-link__icon"></div>
                            <div class="db-sidemenu-link__text-block">
                              <div class="db-sidemenu-link__text">Wallet <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
                            </div>
                          </a> </li>
                        <div class="dropdown-container">
                          <li class="hidden"><span class="capa"><a href="fund_transfer.php?type=1">Fund Transfer</a></span> </li>
                          <li class="hidden"><span class="capa"><a href="fund_transfer3.php?type=1">Self Transfer</a></span> </li>
                          <li class="hidden"><span class="capa"><a href="report_fund_transfer.php">Transfer Report</a></span> </li>
                          <li class="hidden"><span class="capa"><a href="profile.php">Wallet Details</a></span> </li>
                          <!--<li class="hidden" ><span class="capa"><a href="account.php">Account Overview</a></span> </li>-->
                        </div>

                        <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
                            <div class="db-sidemenu-icon"><img src="assets/withdraw.png"></div>
                            <div class="db-sidemenu-link__text-block">
                              <div class="db-sidemenu-link__text">Withdrawal <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
                            </div>
                          </a> </li>
                        <div class="dropdown-container">
                          <li class="hidden"><span class="capa"> <a href="withdrawal_block.php?type=10" class="openMod" data-modal="buy">Fund Withdrawal</a> </span> </li>
                          <li class="hidden"><span class="capa"><a href="report_withdrawal_block.php">Report</a></span> </li>
                        </div>

                        <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="logout.php" class="db-sidemenu-link db-sidemenu-link--settings ">
                            <div class="topline-logout-btn__icon"></div>
                            <div class="db-sidemenu-link__text-block">
                              <div class="db-sidemenu-link__text">Logout</div>
                            </div>
                          </a> </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
              <div class="db-page-right">
                <div class="db-page-topline-block">
                  <div class="db-page-topline">
                    <div class="db-page-topline__left">
                      <div class="topmenu-block">
                        <div class="topmenu priority-nav priority-nav-has-dropdown 224">

                        </div>
                      </div>
                    </div>
                    <div class="db-page-topline__right">
                      <div class="db-page-topline-panel-block">
                        <div class="db-page-topline-panel">
                          <div class="db-page-topline-panel__left">

                            <div class="topline-refill-btn-block"> <a href="profile.php" class="topline-refill-btn">
                                <div class="toplinen"><img src="assets/ranking.png"></div>
                                <div class="topline-refill-btn__text">Rank : <?php echo $reward_arr[$user->reward] ?? 'Starter'; ?></div>
                              </a> </div>

                            <div class="topline-refill-btn-block"> <a href="deposit_block.php" class="topline-refill-btn">
                                <div class="topline-refill-btn__icon"></div>
                                <div class="topline-refill-btn__text">Deposit</div>
                              </a> </div>
                          </div>
                          <div class="db-page-topline-panel__right">
                            <div class="db-page-topline-panel__right__logo">
                              <div class="logo-wrapper"> <a href="" class="logo">
                                  <div class="logo-img"> <img src="assets/logo.png" class="image" alt=""> </div>
                                </a> </div>
                            </div>
                            <div class="db-page-topline-panel__right__content">
                              <div class="topline-lang-panel-block">
                                <div class="topline-lang-panel"> <a href="javascript:void(0)" class="current-lang" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <div class="current-lang__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                                    <div class="current-lang__text">EN</div>
                                  </a>
                                  <div class="topline-lang-dropdown dropdown-menu dropdown-menu-end"> <a href="account/?lang=default" class="lang-link">
                                      <div class="lang-link__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                                      <div class="lang-link__text">EN</div>
                                    </a> </div>
                                </div>
                              </div>
                              <div class="topline-logout-btn-block"> <a href="logout.php" class="topline-logout-btn">
                                  <div class="topline-logout-btn__icon"></div>
                                </a> </div>
                              <div class="mobile-panel-btn-block">
                                <button type="button" class="mobile-panel-btn" onclick="show()"></button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="db-page-content-block">
                  <div class="db-page-content">
                    <div class="refill-block">
                      <div class="refill">
                        <div class="refill-content-block" style="width: 100%; padding : 5px">
                          <div class="box">
                            <div class="h2 refill-form-title" style="background: linear-gradient(114deg, #17c964 0%, #0170ef 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 15px;"><?php echo $title; ?></div>
                            <div class="refill-info-descr" style="margin-bottom: 30px; color: #eaecef;">Complete your investment order for the selected plan.</div>
                            <!-- Investment Plan Information -->
                            <div class="investment-order-card">
                              <div class="plan-info-header">
                                <div class="plan-info-title"><?php echo $row->title; ?></div>
                                <div class="plan-info-details">
                                  <?php echo $row->line1; ?> | <?php echo $row->line2; ?>
                                </div>
                                <div class="plan-stats">
                                  <div class="plan-stat-item">
                                    <div class="plan-stat-label">Minimum</div>
                                    <div class="plan-stat-value"><?php echo SITE_CURRENCY; ?> <?php echo number_format($min); ?></div>
                                  </div>
                                  <div class="plan-stat-item">
                                    <div class="plan-stat-label">Maximum</div>
                                    <div class="plan-stat-value"><?php echo SITE_CURRENCY; ?> <?php echo number_format($max); ?></div>
                                  </div>
                                  <div class="plan-stat-item">
                                    <div class="plan-stat-label">ROI</div>
                                    <div class="plan-stat-value"><?php echo $roi; ?>%</div>
                                  </div>
                                  <div class="plan-stat-item">
                                    <div class="plan-stat-label">Duration</div>
                                    <div class="plan-stat-value"><?php echo $row->duration; ?> Days</div>
                                  </div>
                                  <div class="plan-stat-item">
                                    <div class="plan-stat-label">Available Balance</div>
                                    <div class="plan-stat-value"><?php echo SITE_CURRENCY; ?> <?php echo number_format($user->wallet_topup * 1); ?></div>
                                  </div>
                                </div>
                              </div>

                              <!-- Investment Form -->
                              <form action="invest_now_model.php" method="post" onSubmit="return abc_();">
                                
                                <div class="form-group">
                                    <label for="uid" class="col-sm-3 col-form-label">User *</label>
                                    <select class="form-control" id="uid" name="uid" required="required">
                                        <option value="<?php echo $_SESSION['userid'];?>" selected="selected">-- You --</option>
                                        <?php 
                                        foreach($childs as $ch){
                                            $child = get_user_details($ch);
                                        ?>
                                            <option value="<?php echo $ch;?>"><?php echo $child->login_id . " - " . $child->name;?></option>
                                        <?php 
                                        }
                                        ?>
                                    </select>
                                </div>

                                <?php if ($min != $max) { ?>
                                  <div class="form-group">
                                    <label class="form-label">Investment Amount</label>
                                    <div class="input-group">
                                      <input class="form-control" type="text" id="invest_amount" name="amount" maxlength="20" required="required" placeholder="Enter amount">
                                      <div class="input-group-addon"><?php echo SITE_CURRENCY; ?></div>
                                    </div>
                                    <div class="range-slider">
                                      <input type="range" min="<?php echo $min; ?>" max="<?php echo $max; ?>" value="<?php echo $min; ?>" id="investment_range">
                                      <div class="range-labels">
                                        <span><?php echo $min; ?> <?php echo SITE_CURRENCY; ?></span>
                                        <span><?php echo $max; ?> <?php echo SITE_CURRENCY; ?></span>
                                      </div>
                                    </div>
                                    <div id="calculator_msg" style="margin-top: 10px; color: #848e9c; font-size: 14px;"></div>
                                  </div>
                                <?php } else { ?>
                                  <div class="form-group">
                                    <label class="form-label">Fixed Investment Amount</label>
                                    <div class="input-group">
                                      <input class="form-control" style="color : black" type="text" value="<?php echo $min; ?>" readonly>
                                      <div class="input-group-addon"><?php echo SITE_CURRENCY; ?></div>
                                    </div>
                                  </div>
                                <?php } ?>

                                <div class="form-check">
                                  <input class="form-check-input" type="checkbox" id="terms_check" checked>
                                  <label class="form-check-label" for="terms_check">
                                    I agree to the <a href="#">Terms and Conditions</a>
                                  </label>
                                </div>

                                <input type="hidden" name="recid" value="<?php echo $recid; ?>" />
                                <input type="hidden" id="invest_type" name="type" value="<?php echo $row->type; ?>" />
                                <?php if ($min == $max) { ?>
                                  <input type="hidden" name="amount" value="<?php echo $row->amount_from; ?>" />
                                <?php } ?>
                                <input type="hidden" name="min" value="<?php echo $min; ?>" />
                                <input type="hidden" name="max" value="<?php echo $max; ?>" />

                                <button type="submit" class="invest-submit-btn">
                                  <i class="fas fa-check-circle"></i> Confirm Investment
                                </button>
                              </form>
                            </div>


                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  </div>
  </div>

  <script>
    function show() {
      document.querySelector(".mobile-panel-block").setAttribute("class", "mobile-panel-block active")
    }

    function closeSidebar() {
      document.querySelector(".mobile-panel-block").setAttribute("class", "mobile-panel-block")
    }
  </script>
  <!-- Essential Libraries with CDN fallbacks -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-countup/1.0.0/jquery.countup.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/waypoints/4.0.1/jquery.waypoints.min.js"></script>

  <!-- Try to load local assets, but don't fail if they're missing -->
  <script>
    // Load local assets if available, but don't break if they're missing
    function loadScript(src) {
      var script = document.createElement('script');
      script.src = src;
      script.onerror = function() {
        console.log('Optional script failed to load:', src);
      };
      document.head.appendChild(script);
    }

    // Optional local scripts
    loadScript('./assets/cy/js/common.js?vs=100');
    loadScript('./sidebar.js');
  </script>
  <!-- Main Application Script -->
  <script>
    // Wait for jQuery to be available
    (function() {
      function initializeApp() {
        if (typeof jQuery === 'undefined') {
          console.log('jQuery not available, retrying...');
          setTimeout(initializeApp, 100);
          return;
        }

        console.log('jQuery loaded successfully, version:', $.fn.jquery);

        /* Loop through all dropdown buttons to toggle between hiding and showing its dropdown content */
        var dropdown = document.getElementsByClassName("dropdown-btn");
        var i;

        for (i = 0; i < dropdown.length; i++) {
          dropdown[i].addEventListener("click", function() {
            this.classList.toggle("active");
            this.classList.toggle("dactive");
            var dropdownContent = this.nextElementSibling;
            if (dropdownContent.style.display === "block") {
              dropdownContent.style.display = "none";
            } else {
              dropdownContent.style.display = "block";
            }
          });
        }

        $(document).ready(function() {
          console.log('Document ready - Investment Order page initialized');

          // Handle range slider
          const rangeSlider = document.getElementById('investment_range');
          if (rangeSlider) {
            rangeSlider.addEventListener('input', function() {
              const value = this.value;
              $('#invest_amount').val(value);

              // Calculate percentage for gradient background
              const min = parseInt(this.min);
              const max = parseInt(this.max);
              const percentage = ((value - min) / (max - min)) * 100;

              // Apply gradient background to range slider with project colors
              this.style.background = `linear-gradient(to right, #17c964 0%, #17c964 ${percentage}%, rgba(255,255,255,0.1) ${percentage}%, rgba(255,255,255,0.1) 100%)`;
            });

            // Initialize range slider background
            const value = rangeSlider.value;
            const min = parseInt(rangeSlider.min);
            const max = parseInt(rangeSlider.max);
            const percentage = ((value - min) / (max - min)) * 100;
            rangeSlider.style.background = `linear-gradient(to right, #17c964 0%, #17c964 ${percentage}%, rgba(255,255,255,0.1) ${percentage}%, rgba(255,255,255,0.1) 100%)`;
          }

          // Handle investment amount input
          $("#invest_amount").on('input', function() {
            var amt = $(this).val();

            // Update range slider if it exists
            if (rangeSlider) {
              rangeSlider.value = amt;
              const min = parseInt(rangeSlider.min);
              const max = parseInt(rangeSlider.max);
              const percentage = ((amt - min) / (max - min)) * 100;
              rangeSlider.style.background = `linear-gradient(to right, #17c964 0%, #17c964 ${percentage}%, rgba(255,255,255,0.1) ${percentage}%, rgba(255,255,255,0.1) 100%)`;
            }

            // Call calculator API
            var type = $('#invest_type').val();
            var url = 'calculator_model.php';
            $.post(url, {
              amount: amt,
              type: type
            }, function(result) {
              $("#calculator_msg").html(result);
            });
          });
        });
      }

      // Initialize the app
      initializeApp();
    })();

    // Function to simulate price updates


    // Simple authorization function
    function abc_() {
      return true; // Allow form submission
    }
  </script>

</body>

</html>