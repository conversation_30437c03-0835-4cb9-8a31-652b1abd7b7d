<?php include_once '../lib/config.php';

// Simple setMessage function implementation if not defined
if (!function_exists('setMessage')) {
    function setMessage($message, $type = 'info') {
        $_SESSION['message'] = $message;
        $_SESSION['message_type'] = $type;
    }
}

user();
$uid = $_SESSION['userid'];
$wallet_field_arr = get_wallet_field();
$recid = isset($_POST['recid']) ? (int) tres($_POST['recid']) : 0;
$iRow = my_fetch_object(my_query("SELECT * FROM investments_plan WHERE recid='".$recid."'"));
if(isset($_POST) && $iRow){
    $user = get_user_details($uid);
    $uid2 = isset($_POST['uid']) ? (int) tres($_POST['uid']) : $uid;
    $user_2 = get_user_details($uid2);
    
    $type = (int) tres($_POST['type']);
    $amount = tres($_POST['amount']);
    //$wallet_field = $wallet_field_arr[0];
    $wallet_field = 'wallet_topup';
    $wallet = $user->$wallet_field;
    $wallet2 = $user->wallet_promo;
    
    if($wallet2 > $amount*0.1){
        $wallet2 = $amount*0.1;
    }
    elseif($wallet2 < 0){
        $wallet2 = 0;
    }
    
    
    $min = $iRow->amount_from;
    $max = $iRow->amount_to;
    $mul = $min;
    $mul = 10;
    $recid =$iRow->recid;
    
    if(checkDecimal($amount)==0){
        setMessage('Invalid amount.', 'error');
    }
    elseif(!in_array($type, array(0,1,2,3,4,5,6,7,8,9,10))){
        setMessage('Invalid amount.', 'error');
    }
    elseif($amount < 0){
        setMessage('Invalid amount.', 'error');
    }
    elseif($amount<$min || $amount>$max){
        setMessage('Invest min '.SITE_CURRENCY.''.$min.' and max '.SITE_CURRENCY.''.$max.' multiple of '.$mul.'.', 'error');
    }
    elseif($amount%$mul){
        setMessage('Invest min '.SITE_CURRENCY.''.$min.' and max '.SITE_CURRENCY.''.$max.' multiple of '.$mul.'.', 'error');
    }
    elseif(($wallet+$wallet2)<$amount){
        setMessage('Insufficient funds to cover this investment.', 'error');
    }
    else{
        my_query("UPDATE user SET $wallet_field=$wallet_field-'".($amount-$wallet2)."', wallet_promo=wallet_promo-'".$wallet2."' WHERE uid='".$uid."'");
        
        $_uid = $uid;
        $uid = isset($_POST['uid']) ? (int) tres($_POST['uid']) : $uid;
        
        $camt = round($amount/B_RATE_, 2);
        $bamt = $amount;
        
        $iamount = $amount;
        $_package = ($recid < $user2->package) ? $user2->package : $recid;
        
        if($user_2->topup > 0){
            my_query("UPDATE user SET package='".$_package."', topup=topup+'$amount', wallet_promo=wallet_promo+'".$iamount."' WHERE uid='$uid'");
        }
        else{
            $topupa = ($recid == 1) ? $amount : $amount;
            $tkna = ($recid == 1) ? 0 : 0;
            my_query("UPDATE user SET package='".$_package."', topup=topup+'$topupa', wallet_promo=wallet_promo+'".$iamount."', topup_datetime='".date('c')."' WHERE uid='$uid'");
        }
        
        my_query("INSERT INTO `income_royalty` (`uid`, `amount`, `datetime`, `level`, `type`) VALUES ('".$uid ."', '".$iamount."', '".date('c')."', '".$recid."', '1')");
        
        my_query("INSERT INTO investments (uid, amount, amount2, ipid, datetime, type, uid2, amount_coin, bonus) VALUES ('$uid', '$amount', '$amount', '$recid', '".date('c')."', '$type', '$_uid', '".$camt."', '".$bamt."')");
        
        $ruser = get_user_details($uid);
        $pool = $iRow->recid - 1;
        $pool2 = $pool+1;
        
        /*************************/
        if($recid <= 8 && 0){
            $top = get_top_level_uids2($uid, 1);
    
            $level_amount = array(0, 0.01, 0.02, 0.03, 0.04, 0.06, 0.01, 0.05, 0.05, 0.05, 0.02, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01);
    
            $i = 0;
            $j = 0;
            $_per = 6;
            $level = count($top);
            if($level>25000){$level=25000;}
            if($level>0){
                while($i<$level && $j < 5){
                    $value = $top[$i];
                    $user2 = get_user_details($value);
                    /*$check_d = my_num_rows(my_query("SELECT uid FROM user WHERE refer_id='".$value."' AND status = 0 AND topup > 0"));
                    
                    $reward = $user2->reward;
                    if($user2->package >= 5 && $check_d >= 50){
                        $reward = $user2->package;
                    }
                    elseif($user2->package >= 4 && $check_d >= 20){
                        $reward = $user2->package;
                    }
                    elseif($user2->package >= 3 && $check_d >= 10){
                        $reward = $user2->package;
                    }
                    elseif($user2->package >= 2 && $check_d >= 5){
                        $reward = $user2->package;
                    }
                    elseif($user2->package >= 1 && $check_d >= 0){
                        $reward = $user2->package;
                    }*/
                    
                    /*if($i == 0 && $user2->package >= 1){
                        $new_amount = $amount * 0.05;
                         my_query("UPDATE user SET wallet= wallet+'$new_amount' WHERE uid='".$value."'");
                        my_query("INSERT INTO `income_direct` (`uid`, `from_uid`, `amount`, `datetime`, ipid, iamount) VALUES ('" .$value ."','".$uid."','".$new_amount."','".date('c')."', '".$recid."', '".$iamount."')");
                    }*/
                    
                    /*if($reward > $j){
                        $percentage = $level_amount[$reward] - $level_amount[$j];
                        $j = $reward;
                        //$percentage = $level_amount[$j];
                        $new_amount = $percentage * $amount;
                        $new_amount = check_3x($value, $new_amount);
                        if($user2->topup > 0 && $new_amount > 0){
                            my_query("UPDATE user SET wallet= wallet+'$new_amount' WHERE uid='".$value."'");
        
                            /*if($i==0){
                                my_query("INSERT INTO `income_direct` (`uid`, `from_uid`, `amount`, `datetime`, ipid, iamount) VALUES ('" .$value ."','".$uid."','".$new_amount."','".date('c')."', '".$recid."', '".$iamount."')");
                            }
                            else{*
                                my_query("INSERT INTO `income_level` (`uid`, `from_uid`, `amount`, `datetime`, `level`, ipid, iamount) VALUES ('" .$value ."','".$uid."','".$new_amount."','".date('c')."','".($reward)."', '".$recid."', '".$iamount."')");
                            //}
                        }
                    }*/
                    $i++;
                }
            }
        }
        /*************************/
        
        setMessage('Success - thank you for invest.', 'success');
    }
}

/* get top level uids */
function get_top_level_uids2($uid, $level=0, $arr=array()){
    global $link;
    $result = my_query("SELECT refer_id FROM user WHERE uid = '$uid'");
    if(count($arr)==$level && $level!=0){
        return $arr;
    }elseif($uid==100){
        return $arr;
    }
    if(my_num_rows($result)>0){
        $data = my_fetch_array($result);
        $arr[count($arr)] = $data[0];
        return get_top_level_uids2($data[0], $level, $arr);
    }else {
        return $arr;
    }
}

redirect('./invest_now.php?i='.$recid);
?>