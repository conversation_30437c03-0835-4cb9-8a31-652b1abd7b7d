<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Popup Notification Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #1c2127;
            color: #eaecef;
        }
        .demo-container {
            background: linear-gradient(135deg, #1c2127 0%, #121517 100%);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.05);
            padding: 30px;
            margin-bottom: 30px;
        }
        h1 {
            background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        .test-btn {
            padding: 15px 25px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            text-align: center;
            display: block;
        }
        .btn-success {
            background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
            color: #fff;
        }
        .btn-error {
            background: linear-gradient(114deg, #ff4757 0%, #ff3838 100%);
            color: #fff;
        }
        .btn-warning {
            background: linear-gradient(114deg, #ffa502 0%, #ff6348 100%);
            color: #fff;
        }
        .btn-info {
            background: linear-gradient(114deg, #3742fa 0%, #2f3542 100%);
            color: #fff;
        }
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(23, 201, 100, 0.4);
        }
        .description {
            color: #848e9c;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        .code-block {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>Investment Popup Notification Demo</h1>
        
        <div class="description">
            <p>This demo shows the popup notification system that has been added to the <code>invest_now.php</code> page. 
            The notifications will appear based on the messages set in <code>invest_now_model.php</code>.</p>
            
            <p>Click any of the buttons below to test different types of notifications:</p>
        </div>

        <div class="test-buttons">
            <a href="test_popup.php?type=success" class="test-btn btn-success">
                Test Success Message
            </a>
            <a href="test_popup.php?type=error" class="test-btn btn-error">
                Test Error Message
            </a>
            <a href="test_popup.php?type=warning" class="test-btn btn-warning">
                Test Warning Message
            </a>
            <a href="test_popup.php?type=invalid" class="test-btn btn-info">
                Test Invalid Amount
            </a>
        </div>

        <div class="description">
            <h3 style="color: #17c964; margin-top: 30px;">How it works:</h3>
            
            <p><strong>1. Message Setting:</strong> In <code>invest_now_model.php</code>, messages are set using:</p>
            <div class="code-block">
setMessage('Success - thank you for invest.', 'success');<br>
setMessage('Insufficient funds to cover this investment.', 'error');<br>
setMessage('Invalid amount.', 'error');
            </div>

            <p><strong>2. Message Display:</strong> In <code>invest_now.php</code>, messages are retrieved and displayed as Toastr popups:</p>
            <div class="code-block">
// PHP retrieves the message from session<br>
if (isset($_SESSION['message'])) {<br>
&nbsp;&nbsp;$popup_message = $_SESSION['message'];<br>
&nbsp;&nbsp;$popup_type = $_SESSION['message_type'];<br>
}<br><br>
// JavaScript displays the popup<br>
toastr.success('Message', 'Success');<br>
toastr.error('Message', 'Error');
            </div>

            <p><strong>3. Message Types:</strong></p>
            <ul>
                <li><span style="color: #17c964;">success</span> - Green popup for successful operations</li>
                <li><span style="color: #ff4757;">error</span> - Red popup for errors and validation failures</li>
                <li><span style="color: #ffa502;">warning</span> - Orange popup for warnings</li>
                <li><span style="color: #3742fa;">info</span> - Blue popup for general information</li>
            </ul>
        </div>
    </div>
</body>
</html>
