# Popup Notification System for Investment Page

## Overview
A popup notification system has been successfully added to the `invest_now.php` page to display messages from `invest_now_model.php` using Toastr.js library. The system integrates with the existing `getMessage()` function that uses `$_SESSION['SetMessage']`.

## Files Modified

### 1. `invest_now.php`
- Added session message retrieval logic for popups
- Added `setMessage()` function implementation that works with existing `getMessage()`
- Added Toastr.js popup display code
- Added custom CSS styling for notifications
- Added `<?php echo getMessage(); ?>` for HTML alert display

### 2. `invest_now_model.php`
- Added `setMessage()` function implementation
- Messages are already being set using `setMessage()` calls

## How It Works

### Message Setting (invest_now_model.php)
```php
// Success message
setMessage('Success - thank you for invest.', 'success');

// Error messages
setMessage('Invalid amount.', 'error');
setMessage('Insufficient funds to cover this investment.', 'error');
setMessage('Invest min $100 and max $10000 multiple of 10.', 'error');
```

### Message Display (invest_now.php)
The `setMessage()` function now does two things:
1. Creates HTML alert for `getMessage()` function (stored in `$_SESSION['SetMessage']`)
2. Stores message data for popup notifications (stored in `$_SESSION['popup_message']` and `$_SESSION['popup_type']`)

```php
// HTML Alert Display (existing system)
<?php echo getMessage(); ?>

// Popup notification retrieval
if (isset($_SESSION['popup_message'])) {
    $popup_message = $_SESSION['popup_message'];
    $popup_type = $_SESSION['popup_type'];
    // Clear popup message after retrieval
    unset($_SESSION['popup_message']);
    unset($_SESSION['popup_type']);
}
```

```javascript
// Display popup based on type
toastr.success('Message', 'Success');
toastr.error('Message', 'Error');
toastr.warning('Message', 'Warning');
toastr.info('Message', 'Information');
```

## Message Types

| Type | Color | Usage |
|------|-------|-------|
| `success` | Green | Successful operations (investment completed) |
| `error` | Red | Validation errors, insufficient funds |
| `warning` | Orange | Warnings and cautions |
| `info` | Blue | General information |

## Testing

### Demo Files Created:
1. **`popup_demo.html`** - Visual demo page with test buttons
2. **`test_popup.php`** - Test script to trigger different notification types

### Test the System:
1. Open `popup_demo.html` in your browser
2. Click any test button to see different notification types
3. Or directly visit: `test_popup.php?type=success` (or error, warning, invalid)

### Manual Testing:
1. Submit the investment form with invalid data to see error popups
2. Submit with valid data to see success popup
3. Try different validation scenarios

## Existing Messages in invest_now_model.php

The following messages are already implemented and will show as popups:

### Error Messages:
- "Invalid amount." (for decimal/validation issues)
- "Invest min $X and max $Y multiple of Z." (for amount range issues)
- "Insufficient funds to cover this investment." (for wallet balance issues)

### Success Message:
- "Success - thank you for invest." (for successful investment)

## Customization

### Styling:
The notifications use custom CSS that matches the existing theme:
- Gradient backgrounds matching the site's color scheme
- Proper positioning (top-right corner)
- Custom border and shadow effects

### Toastr Options:
```javascript
toastr.options = {
    "closeButton": true,
    "progressBar": true,
    "positionClass": "toast-top-right",
    "timeOut": "5000",
    // ... other options
};
```

## Dependencies

- **Toastr.js** - Already included via CDN in the page
- **jQuery** - Already included via CDN in the page
- **Session support** - PHP sessions for message storage

## Browser Compatibility

The popup system works in all modern browsers that support:
- CSS3 gradients
- JavaScript ES5+
- HTML5 session storage

## Notes

- Messages are automatically cleared after being displayed once
- The system is backward compatible with existing code
- No database changes required
- Uses existing Toastr.js library already loaded on the page
