<?php
session_start();
header('Content-Type: application/json');

$email = $_POST['email'] ?? '';
$otp = $_POST['otp'] ?? '';
$response = ['success' => false, 'message' => ''];

// Verify OTP (simple implementation - you might want to add expiration check)
if(isset($_SESSION['email_otp']) && 
   isset($_SESSION['email_otp_email']) && 
   $_SESSION['email_otp_email'] == $email &&
   $_SESSION['email_otp'] == $otp) {
    
    $response['success'] = true;
    $response['message'] = 'OTP verified';
    $_SESSION['email_verified'] = true;
} else {
    $response['message'] = 'Invalid OTP';
}

echo json_encode($response);
?>