<?php
session_start();
include_once '../lib/config.php';

// Check if user is logged in
if (!isset($_SESSION['userid']) || empty($_SESSION['userid'])) {
  header('Location: index.php');
  exit;
}

$uid = $_SESSION['userid'];
$user = get_user_details($uid);

// Debug: Check if user data is retrieved
if (!$user) {
  echo "Error: User data not found for UID: " . $uid;
  exit;
}

$reward_arr = get_reward();
$title = 'Join Our Affiliate Program';
$query = "SELECT * FROM investments_plan WHERE status = 0 ORDER BY recid ASC";
$result = my_query($query);
$i = 0;
$j = 0;
?>
<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <title><?php echo $title; ?> - <?php echo SITE_NAME; ?></title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <link rel="stylesheet" href="./assets/cy/libs/bootstrap/css/bootstrap.min.css?vs=100">
  <link rel="icon" type="image/png" href="./assets/fav.png" sizes="96x96" />
  <link rel="stylesheet" href="./assets/cy/libs/priority-navigation-master/dist/priority-nav-core.css?vs=100">
  <link rel="stylesheet" href="./assets/cy/libs/swiper/swiper.min.css?vs=100">
  <link rel="stylesheet" href="./assets/cy/libs/bootstrap-select-1.14.0/dist/css/bootstrap-select.min.css?vs=100">
  <link rel="stylesheet" href="./assets/cy/libs/plyr/dist/plyr.css?vs=100">
  <link rel="stylesheet" href="./assets/cy/libs/ion.rangeSlider-master/css/ion.rangeSlider.min.css?vs=100">
  <link rel="stylesheet" href="./assets/cy/libs/toastr-master/build/toastr.min.css?vs=100">
  <link rel="stylesheet" href="./assets/cy/css/main.css?vs=100">
  <link rel="stylesheet" href="./assets/cy/css/media.css?vs=100">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="shortcut icon" href="./assets/fav.png" />

  <style>
    .openUserInfo {
      cursor: pointer;
    }

    .sectionLotteriesItemsSMEmpty {
      min-height: 46px;
    }

    button.change-pswd-type-link {
      border: 0px;
    }

    .logo-img img {
      width: 150px;
    }

    .db-page--wide-menu .db-side-logo__text-block {
      width: 200px !important;
    }

    .db-side-logo__text {
      padding-left: 0 !important;
    }

    .mobile-panel-top-left .logo-wrapper {
      display: flex;
      max-width: 150px;
    }

    .db-side-logo__text .image {
      width: 150px;
      min-width: 61px;
      max-width: none;
    }

    .db-page-topline-panel__right__logo .logo-wrapper {
      max-width: 160px;
    }

    .topline-refill-btn {
      line-height: 1;
      color: #EBF4FF;
      font-size: 15px;
      text-align: center;
      height: 40px;
    }

    .topline-refill-btn {
      width: 170px;
    }

    .toplinen img {
      width: 17px;
    }

    @media only screen and (min-width: 601px) {
      .db-page-topline__right {
        margin-left: -200px;
      }
    }

    .dropdown-container {
      margin-top: 5px;
      border-radius: 5px;
      padding-bottom: 5px;
    }

    .dropdown-container {
      display: none;
      border-radius: 5px;
    }

    @media screen and (max-width: 780px) {
      .mobhidden {
        display: none !important;
      }

      .cabMenu li.hidden {
        display: flex !important;
      }

      .langList {
        display: none
      }
    }

    .dropdown-btn .fa {
      position: absolute;
      right: 5px;
      top: 65px;
      font-size: 20px;
      color: #fff;
    }

    .fa-caret-up {
      display: none;
    }

    .dactive .fa-caret-down {
      display: none;
    }

    .dactive .fa-caret-up {
      display: inline-block !important;
    }

    .dropdown-container li {
      height: auto;
      padding: 5px 10px 5px 10px;
      line-height: 1;
    }

    .dropdown-containers li:hover {
      background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
      ;
      color: #fff;
    }

    .dropdown-container li:hover cap {
      color: #fff;
    }

    .hidden:hover {
      background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
    }

    .capa a {
      margin-left: 20px;
      color: #fff;
      text-decoration: none;
      font-size: 14px;
    }

    .db-sidemenu-icon img {
      width: 25px;
    }

    .db-sidemenu-icon {
      position: inherit;
    }

    .mobile-db-menu-link__text {
      margin-left: 5px;
    }
  </style>

</head>

<body class="page page--main">
  <div class="page-inner">
    <div class="page-progress-block xProgress d-none">
      <div class="page-progress">
        <div class="page-progress__progress xProgressPercent" style="width: 0%;"></div>
      </div>
    </div>
    <div class="mobile-panel-block">
      <div class="mobile-panel-top-block">
        <div class="mobile-panel-top-left">
          <div class="logo-wrapper"> <a href="" class="logo">
              <div class="logo-img"> <img class="image" src="assets/logo.png" alt=""> </div>
            </a> </div>
        </div>
        <div class="mobile-panel-top-right">
          <div class="topline-lang-panel-block">
            <div class="topline-lang-panel"> <a href="javascript:void(0)" class="current-lang" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                <div class="current-lang__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                <div class="current-lang__text">EN</div>
              </a>
              <div class="topline-lang-dropdown dropdown-menu dropdown-menu-end"> <a href="account/?lang=default" class="lang-link">
                  <div class="lang-link__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                  <div class="lang-link__text">EN</div>
                </a> </div>
            </div>
          </div>
          <div class="mobile-panel-close-btn-block">
            <button type="button" class="mobile-panel-close-btn" onclick="closeSidebar()"></button>
          </div>
        </div>
      </div>
      <div class="mobile-db-menu-block">
        <div class="mobile-db-menu">
          <ul>
            <li class="mobile-db-menu-item mobile-db-menu-item--dashboard"> <a href='dashboard.php' class="mobile-db-menu-link mobile-db-menu-link--dashboard">
                <div class="mobile-db-menu-link__icon"></div>
                <div class="mobile-db-menu-link__text-block">
                  <div class="mobile-db-menu-link__text">Dashboard</div>
                </div>
              </a> </li>
            <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings ">
                <div class="mobile-db-menu-link__icon"></div>
                <div class="mobile-db-menu-link__text-block">
                  <div class="mobile-db-menu-link__text">Account <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
                </div>
              </a> </li>
            <div class="dropdown-container">
              <li class="hidden"><span class="capa"> <a href="profile.php" class="openMod" data-modal="buy">Profile</a> </span> </li>
              <li class="hidden"><span class="capa"><a href="change_password.php">Change Password</a></span> </li>
            </div>
            <li class="mobile-db-menu-item mobile-db-menu-item--refill dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--refill">
                <div class="mobile-db-menu-link__icon"></div>
                <div class="mobile-db-menu-link__text-block">
                  <div class="mobile-db-menu-link__text">Deposit <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
                </div>
              </a> </li>
            <div class="dropdown-container">
              <li class="hidden"><span class="capa"> <a href="deposit_block.php" class="openMod" data-modal="buy">Add Fund</a> </span> </li>
              <li class="hidden"><span class="capa"><a href="report_deposit_block.php">Report</a></span> </li>
            </div>
            <li class="mobile-db-menu-item mobile-db-menu-item--withdrawal dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--withdrawal active">
                <div class="mobile-db-menu-link__icon"></div>
                <div class="mobile-db-menu-link__text-block">
                  <div class="mobile-db-menu-link__text">Activation <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
                </div>
              </a> </li>
            <div class="dropdown-container">
              <li class="hidden"><span class="capa"> <a href="invest.php" class="openMod" data-modal="buy">Activate</a> </span> </li>
              <li class="hidden"><span class="capa"><a href="report_invest.php">Report</a></span> </li>
            </div>
            <li class="mobile-db-menu-item mobile-db-menu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--deposits ">
                <div class="db-sidemenu-icon"><img src="assets/network.png"></div>
                <div class="mobile-db-menu-link__text-block">
                  <div class="mobile-db-menu-link__text">Network <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
                </div>
              </a> </li>
            <div class="dropdown-container">
              <li class="hidden"><span class="capa"> <a href="direct_referral_left.php" class="openMod" data-modal="buy">Left Direct</a> </span> </li>
              <li class="hidden"><span class="capa"><a href="direct_referral.php">Right Direct</a></span> </li>
              <li class="hidden"><span class="capa"><a href="downline.php">Total Team</a></span> </li>
              <li class="hidden"><span class="capa"><a href="tree_view.php">Tree View</a></span> </li>
            </div>
            <li class="mobile-db-menu-item mobile-db-menu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--deposits ">
                <div class="mobile-db-menu-link__icon"></div>
                <div class="mobile-db-menu-link__text-block">
                  <div class="mobile-db-menu-link__text">Earning <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
                </div>
              </a> </li>
            <div class="dropdown-container">
              <!--<li class="hidden" ><span class="capa"> <a href="report_direct.php" class="openMod" data-modal="buy">Non-Working Bonus</a> </span> </li>-->
              <li class="hidden"><span class="capa"><a href="report_growth.php" class="openMod" data-modal="buy">Non-Working Bonus</a></span> </li>
              <li class="hidden"><span class="capa"><a href="report_royalty.php?type=1">Lifetime Cash Back Income</a></span> </li>
              <li class="hidden"><span class="capa"><a href="report_binary.php">Matching Bonus</a></span> </li>
              <li class="hidden"><span class="capa"><a href="report_royalty.php">Reward Bonus</a></span> </li>
              <!--<li class="hidden" ><span class="capa"><a href="report_all.php">All Reports</a></span> </li>-->
            </div>
            <li class="mobile-db-menu-item mobile-db-menu-item--affiliate dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--affiliate ">
                <div class="mobile-db-menu-link__icon"></div>
                <div class="mobile-db-menu-link__text-block">
                  <div class="mobile-db-menu-link__text">Wallet <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
                </div>
              </a> </li>
            <div class="dropdown-container">
              <!--<li class="hidden" ><span class="capa"><a href="fund_transfer.php?type=1">Fund Transfer</a></span> </li>-->
              <!--<li class="hidden" ><span class="capa"><a href="fund_transfer3.php?type=1">Self Transfer</a></span> </li>-->
              <li class="hidden"><span class="capa"><a href="report_fund_transfer.php">Transfer Report</a></span> </li>
              <li class="hidden"><span class="capa"><a href="profile.php">Wallet Details</a></span> </li>
              <!--<li class="hidden" ><span class="capa"><a href="account.php">Account Overview</a></span> </li>-->
            </div>
            <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings ">
                <div class="db-sidemenu-icon"><img src="assets/withdraw.png"></div>
                <div class="mobile-db-menu-link__text-block">
                  <div class="mobile-db-menu-link__text">Withdrawal <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
                </div>
              </a> </li>
            <div class="dropdown-container">
              <li class="hidden"><span class="capa"> <a href="withdrawal_block.php?type=10" class="openMod" data-modal="buy">Fund Withdrawal</a> </span> </li>
              <li class="hidden"><span class="capa"><a href="report_withdrawal_block.php">Report</a></span> </li>
            </div>
            <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings ">
                <div class="db-sidemenu-icon"><img src="assets/lotery.png"></div>
                <div class="mobile-db-menu-link__text-block">
                  <div class="mobile-db-menu-link__text">Lotteries <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
                </div>
              </a> </li>
            <div class="dropdown-container">
              <li class="hidden"><span class="capa"> <a href="#" class="openMod" data-modal="buy">Buy Lottery</a> </span> </li>
              <li class="hidden"><span class="capa"><a href="#">Report</a></span> </li>
            </div>
            <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings ">
                <div class="db-sidemenu-icon"><img src="assets/gaming.png"></div>
                <div class="mobile-db-menu-link__text-block">
                  <div class="mobile-db-menu-link__text">Gaming <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
                </div>
              </a> </li>
            <div class="dropdown-container">
              <li class="hidden"><span class="capa"> <a href="#" class="openMod" data-modal="buy">Play Games</a> </span> </li>
              <li class="hidden"><span class="capa"><a href="#">Report</a></span> </li>
            </div>
            <li class="mobile-db-menu-item mobile-db-menu-item--settings"> <a href='#' class="mobile-db-menu-link mobile-db-menu-link--settings">
                <div class="db-sidemenu-icon"><img src="assets/promo.png"></div>
                <div class="mobile-db-menu-link__text-block">
                  <div class="mobile-db-menu-link__text">Promo Stuff</div>
                </div>
              </a> </li>
            <li class="mobile-db-menu-item mobile-db-menu-item--settings"> <a href='#' class="mobile-db-menu-link mobile-db-menu-link--settings">
                <div class="db-sidemenu-icon"><img src="assets/support.png"></div>
                <div class="mobile-db-menu-link__text-block">
                  <div class="mobile-db-menu-link__text">Support Ticket</div>
                </div>
              </a> </li>
            <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="logout.php" class="mobile-db-menu-link mobile-db-menu-link--settings ">
                <div class="topline-logout-btn__icon"></div>
                <div class="mobile-db-menu-link__text-block">
                  <div class="mobile-db-menu-link__text">Logout</div>
                </div>
              </a> </li>
          </ul>
        </div>
      </div>
      <div class="mobile-panel-menu-block">
        <div class="mobile-panel-menu">
          <div class="mobile-menu">

          </div>
        </div>
      </div>
    </div>

    <div class="db-page-block">
      <div class="container">
        <div class="row">
          <div class="col-12">
            <div class="db-page db-page--wide-menu">
              <div class="db-page-left">
                <div class="db-side-block">
                  <div class="db-side-logo-block"> <a href="" class="db-side-logo">
                      <div class="db-side-logo__text-block">
                        <div class="db-side-logo__text"> <img class="image" src="assets/logo.png" alt=""> </div>
                      </div>
                    </a> </div>
                  <div class="db-side-toggle-panel-btn-block">
                    <button class="db-side-toggle-panel-btn">
                      <div class="db-side-toggle-panel-btn__text-block">
                        <div class="db-side-toggle-panel-btn__text">Navigation</div>
                      </div>
                      <div class="db-side-toggle-panel-btn__icon"></div>
                    </button>
                  </div>
                  <div class="db-sidemenu-block">
                    <div class="db-sidemenu">
                      <ul>
                        <li class="db-sidemenu-item db-sidemenu-item--dashboard"> <a href='dashboard.php' class="db-sidemenu-link db-sidemenu-link--dashboard">
                            <div class="db-sidemenu-link__icon"></div>
                            <div class="db-sidemenu-link__text-block">
                              <div class="db-sidemenu-link__text">Dashboard</div>
                            </div>
                          </a> </li>
                        <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
                            <div class="db-sidemenu-link__icon"></div>
                            <div class="db-sidemenu-link__text-block">
                              <div class="db-sidemenu-link__text">Account <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
                            </div>
                          </a> </li>
                        <div class="dropdown-container">
                          <li class="hidden"><span class="capa"> <a href="profile.php" class="openMod" data-modal="buy">Profile</a> </span> </li>
                          <li class="hidden"><span class="capa"><a href="change_password.php">Change Password</a></span> </li>
                        </div>

                        <li class="db-sidemenu-item db-sidemenu-item--refill dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--refill">
                            <div class="db-sidemenu-link__icon"></div>
                            <div class="db-sidemenu-link__text-block">
                              <div class="db-sidemenu-link__text">Deposit <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
                            </div>
                          </a> </li>
                        <div class="dropdown-container">
                          <li class="hidden"><span class="capa"> <a href="deposit_block.php" class="openMod" data-modal="buy">Add Fund</a> </span> </li>
                          <li class="hidden"><span class="capa"><a href="report_deposit_block.php">Report</a></span> </li>
                        </div>

                        <li class="db-sidemenu-item db-sidemenu-item--withdrawal dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--withdrawal active">
                            <div class="db-sidemenu-link__icon"></div>
                            <div class="db-sidemenu-link__text-block">
                              <div class="db-sidemenu-link__text">Activation <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
                            </div>
                          </a> </li>
                        <div class="dropdown-container">
                          <li class="hidden"><span class="capa"> <a href="invest.php" class="openMod" data-modal="buy">Activate</a> </span> </li>
                          <li class="hidden"><span class="capa"><a href="report_invest.php">Report</a></span> </li>
                        </div>

                        <li class="db-sidemenu-item db-sidemenu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--deposits ">
                            <div class="db-sidemenu-icon"><img src="assets/network.png"></div>
                            <div class="db-sidemenu-link__text-block">
                              <div class="db-sidemenu-link__text">Network <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
                            </div>
                          </a> </li>
                        <div class="dropdown-container">
                          <li class="hidden"><span class="capa"> <a href="direct_referral_left.php" class="openMod" data-modal="buy">Left Direct</a> </span> </li>
                          <li class="hidden"><span class="capa"><a href="direct_referral.php">Right Direct</a></span> </li>
                          <li class="hidden"><span class="capa"><a href="downline.php">Total Team</a></span> </li>
                          <li class="hidden"><span class="capa"><a href="tree_view.php">Tree View</a></span> </li>
                        </div>

                        <li class="db-sidemenu-item db-sidemenu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--deposits ">
                            <div class="db-sidemenu-link__icon"></div>
                            <div class="db-sidemenu-link__text-block">
                              <div class="db-sidemenu-link__text">Earning <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
                            </div>
                          </a> </li>
                        <div class="dropdown-container">
                          <!--<li class="hidden" ><span class="capa"> <a href="report_direct.php" class="openMod" data-modal="buy">Non-Working Bonus</a> </span> </li>-->
                          <li class="hidden"><span class="capa"><a href="report_growth.php" class="openMod" data-modal="buy">Non-Working Bonus</a></span> </li>
                          <li class="hidden"><span class="capa"><a href="report_royalty.php?type=1">Lifetime Cash Back Income</a></span> </li>
                          <li class="hidden"><span class="capa"><a href="report_binary.php">Matching Bonus</a></span> </li>
                          <li class="hidden"><span class="capa"><a href="report_royalty.php">Reward Bonus</a></span> </li>
                          <!--<li class="hidden" ><span class="capa"><a href="report_all.php">All Reports</a></span> </li>-->

                        </div>
                        <li class="db-sidemenu-item db-sidemenu-item--affiliate dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--affiliate ">
                            <div class="db-sidemenu-link__icon"></div>
                            <div class="db-sidemenu-link__text-block">
                              <div class="db-sidemenu-link__text">Wallet <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
                            </div>
                          </a> </li>
                        <div class="dropdown-container">
                          <!--<li class="hidden" ><span class="capa"><a href="fund_transfer.php?type=1">Fund Transfer</a></span> </li>-->
                          <!--<li class="hidden" ><span class="capa"><a href="fund_transfer3.php?type=1">Self Transfer</a></span> </li>-->
                          <li class="hidden"><span class="capa"><a href="report_fund_transfer.php">Transfer Report</a></span> </li>
                          <li class="hidden"><span class="capa"><a href="profile.php">Wallet Details</a></span> </li>
                          <!--<li class="hidden" ><span class="capa"><a href="account.php">Account Overview</a></span> </li>-->
                        </div>

                        <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
                            <div class="db-sidemenu-icon"><img src="assets/withdraw.png"></div>
                            <div class="db-sidemenu-link__text-block">
                              <div class="db-sidemenu-link__text">Withdrawal <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
                            </div>
                          </a> </li>
                        <div class="dropdown-container">
                          <li class="hidden"><span class="capa"> <a href="withdrawal_block.php?type=10" class="openMod" data-modal="buy">Fund Withdrawal</a> </span> </li>
                          <li class="hidden"><span class="capa"><a href="report_withdrawal_block.php">Report</a></span> </li>
                        </div>

                        <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
                            <div class="db-sidemenu-icon"><img src="assets/lotery.png"></div>
                            <div class="db-sidemenu-link__text-block">
                              <div class="db-sidemenu-link__text">Lotteries <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
                            </div>
                          </a> </li>
                        <div class="dropdown-container">
                          <li class="hidden"><span class="capa"> <a href="#" class="openMod" data-modal="buy">Buy Lottery</a> </span> </li>
                          <li class="hidden"><span class="capa"><a href="#">Report</a></span> </li>
                        </div>
                        <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
                            <div class="db-sidemenu-icon"><img src="assets/gaming.png"></div>
                            <div class="db-sidemenu-link__text-block">
                              <div class="db-sidemenu-link__text">Gaming <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
                            </div>
                          </a> </li>
                        <div class="dropdown-container">
                          <li class="hidden"><span class="capa"> <a href="#" class="openMod" data-modal="buy">Play Games</a> </span> </li>
                          <li class="hidden"><span class="capa"><a href="#">Report</a></span> </li>
                        </div>
                        <li class="db-sidemenu-item db-sidemenu-item--settings"> <a href='#' class="db-sidemenu-link db-sidemenu-link--settings">
                            <div class="db-sidemenu-icon"><img src="assets/promo.png"></div>
                            <div class="db-sidemenu-link__text-block">
                              <div class="db-sidemenu-link__text">Promo Stuff </div>
                            </div>
                          </a> </li>
                        <li class="db-sidemenu-item db-sidemenu-item--settings"> <a href='#' class="db-sidemenu-link db-sidemenu-link--settings">
                            <div class="db-sidemenu-icon"><img src="assets/support.png"></div>
                            <div class="db-sidemenu-link__text-block">
                              <div class="db-sidemenu-link__text">Support Ticket </div>
                            </div>
                          </a> </li>

                        <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="logout.php" class="db-sidemenu-link db-sidemenu-link--settings ">
                            <div class="topline-logout-btn__icon"></div>
                            <div class="db-sidemenu-link__text-block">
                              <div class="db-sidemenu-link__text">Logout</div>
                            </div>
                          </a> </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
              <div class="db-page-right">
                <div class="db-page-topline-block">
                  <div class="db-page-topline">
                    <div class="db-page-topline__left">
                      <div class="topmenu-block">
                        <div class="topmenu priority-nav priority-nav-has-dropdown 224">

                        </div>
                      </div>
                    </div>
                    <div class="db-page-topline__right">
                      <div class="db-page-topline-panel-block">
                        <div class="db-page-topline-panel">
                          <div class="db-page-topline-panel__left">

                            <div class="topline-refill-btn-block"> <a href="profile.php" class="topline-refill-btn">
                                <div class="toplinen"><img src="assets/ranking.png"></div>
                                <div class="topline-refill-btn__text">Rank : <?php echo $reward_arr[$user->reward] ?? 'Starter'; ?></div>
                              </a> </div>

                            <div class="topline-refill-btn-block"> <a href="deposit_block.php" class="topline-refill-btn">
                                <div class="topline-refill-btn__icon"></div>
                                <div class="topline-refill-btn__text">Deposit</div>
                              </a> </div>
                          </div>
                          <div class="db-page-topline-panel__right">
                            <div class="db-page-topline-panel__right__logo">

                              <div class="logo-wrapper"> <a href="" class="logo">
                                  <div class="logo-img"> <img class="image" src="assets/logo.png" alt=""> </div>
                                </a> </div>
                            </div>
                            <div class="topline-lang-panel-block">
                              <div class="topline-lang-panel"> <a href="javascript:void(0)" class="current-lang" role="button" data-bs-toggle="dropdown"
                                  aria-expanded="false">
                                  <div class="current-lang__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                                  <div class="current-lang__text">EN</div>
                                </a>
                                <div class="topline-lang-dropdown dropdown-menu dropdown-menu-end"> <a href="account/?lang=default" class="lang-link">
                                    <div class="lang-link__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                                    <div class="lang-link__text">EN</div>
                                  </a> </div>
                              </div>
                            </div>
                            <!--  <div class="topline-user-panel-block">-->
                            <!--    <div class="topline-user-panel"> <a href="javascript:void(0)" class="topline-user" role="button" data-bs-toggle="dropdown"-->
                            <!--aria-expanded="false">-->
                            <!--      <div class="topline-user__icon"> <img class="image" src="assets/cy/images/svg/user-icon.svg" alt=""> </div>-->
                            <!--      <div class="topline-user__text"><?php echo isset($user->name) ? $user->name : 'User'; ?></div>-->
                            <!--      </a>-->
                            <!--      <div class="topline-user-dropdown dropdown-menu dropdown-menu-end"> <a href="profile.php" class="user-link">-->
                            <!--        <div class="user-link__icon"> <img class="image" src="assets/cy/images/svg/user-icon.svg" alt=""> </div>-->
                            <!--        <div class="user-link__text">Profile</div>-->
                            <!--        </a> <a href="logout.php" class="user-link">-->
                            <!--        <div class="user-link__icon"> <img class="image" src="assets/cy/images/svg/logout-icon.svg" alt=""> </div>-->
                            <!--        <div class="user-link__text">Logout</div>-->
                            <!--        </a> </div>-->
                            <!--    </div>-->
                            <!--  </div>-->
                            <div class="topline-logout-btn-block"> <a href="logout.php" class="topline-logout-btn">
                                <div class="topline-logout-btn__icon"></div>
                              </a> </div>
                            <div class="mobile-panel-btn-block">
                              <button type="button" class="mobile-panel-btn" onclick="show()"></button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="db-page-content-block">
                  <div class="db-page-content">
                    <div class="refill-block">
                      <div class="refill">
                        <div class="refill-content-block" style="width: 100%; padding:0px;">
                          <div class="box">
                            <div class="h2 refill-form-title" style="background: linear-gradient(114deg, #17c964 0%, #0170ef 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 15px;"><?php echo $title; ?></div>

                            <div class="refill-info-descr" style="margin-bottom: 30px; color: #eaecef;">Choose your investment plan and start earning with our secure platform.</div>

                            <style>
                              /* Investment Plan Styling - Matching UI Framework Colors */
                              .investment-plan-card {
                                background: linear-gradient(135deg, #1c2127 0%, #121517 100%);
                                border-radius: 12px;
                                border: 1px solid rgba(255, 255, 255, 0.05);
                                padding: 30px;
                                margin-bottom: 30px;
                                transition: all 0.3s ease;
                                height: 100%;
                                display: flex;
                                flex-direction: column;
                                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                                position: relative;
                                overflow: hidden;
                              }

                              .investment-plan-card::before {
                                content: '';
                                position: absolute;
                                top: 0;
                                left: 0;
                                right: 0;
                                height: 1px;
                                background: linear-gradient(114deg, #0170ef 0%, #17c964 100%);
                              }

                              .investment-plan-card:hover {
                                transform: translateY(-5px);
                                background: rgba(255, 255, 255, 0.08);
                                box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
                                border-color: rgba(23, 201, 100, 0.3);
                              }

                              .plan-header {
                                text-align: center;
                                margin-bottom: 25px;
                              }

                              .plan-title {
                                font-size: 1.8rem;
                                font-weight: 600;
                                background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
                                -webkit-background-clip: text;
                                -webkit-text-fill-color: transparent;
                                background-clip: text;
                                margin-bottom: 15px;
                              }

                              .plan-amount {
                                font-size: 1.3rem;
                                color: #eaecef;
                                margin-bottom: 20px;
                                font-weight: 500;
                              }

                              .plan-features {
                                list-style: none;
                                padding: 0;
                                margin: 25px 0;
                                flex: 1;
                              }

                              .plan-features li {
                                padding: 12px 0;
                                color: #eaecef;
                                border-bottom: 1px solid rgba(255, 255, 255, 0.05);
                                font-size: 1.1rem;
                                transition: all 0.2s ease;
                              }

                              .plan-features li:before {
                                content: '✓';
                                background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
                                -webkit-background-clip: text;
                                -webkit-text-fill-color: transparent;
                                background-clip: text;
                                font-weight: bold;
                                margin-right: 12px;
                                font-size: 1.2rem;
                              }

                              .plan-features li:last-child {
                                border-bottom: none;
                              }

                              .invest-btn {
                                width: 100%;
                                padding: 15px 25px;
                                background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
                                color: #fff;
                                border: none;
                                border-radius: 8px;
                                font-weight: 600;
                                font-size: 1.1rem;
                                cursor: pointer;
                                transition: all 0.3s ease;
                                text-transform: uppercase;
                                letter-spacing: 0.5px;
                                margin-top: auto;
                              }

                              .invest-btn:hover {
                                transform: translateY(-2px);
                                box-shadow: 0 4px 15px rgba(23, 201, 100, 0.4);
                                background: linear-gradient(114deg, #0170ef 0%, #17c964 100%);
                              }

                              /* Large screen - 3 equal columns */
                              @media (min-width: 992px) {
                                .investment-plans-container .row {
                                  display: flex;
                                  flex-wrap: wrap;
                                }

                                .investment-plans-container .col-lg-4 {
                                  flex: 0 0 33.333333%;
                                  max-width: 33.333333%;
                                }

                                .investment-plan-card {
                                  min-height: 400px;
                                  padding: 35px;
                                }

                                .plan-title {
                                  font-size: 2rem;
                                  background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
                                  -webkit-background-clip: text;
                                  -webkit-text-fill-color: transparent;
                                  background-clip: text;
                                }

                                .plan-amount {
                                  font-size: 1.4rem;
                                }

                                .plan-features li {
                                  font-size: 1.15rem;
                                  padding: 15px 0;
                                }

                                .invest-btn {
                                  padding: 18px 30px;
                                  font-size: 1.2rem;
                                  background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
                                }

                                .invest-btn:hover {
                                  background: linear-gradient(114deg, #0170ef 0%, #17c964 100%);
                                }
                              }

                              /* Tablet - 2 columns */
                              @media (min-width: 768px) and (max-width: 991px) {
                                .investment-plan-card {
                                  min-height: 350px;
                                }
                              }

                              /* Mobile - 1 column (default Bootstrap behavior) */
                              @media (max-width: 767px) {
                                .investment-plan-card {
                                  margin-bottom: 20px;
                                }
                              }
                            </style>




                            <!-- Investment Plans Section Header -->
                            <div style="margin-bottom: 30px; padding-bottom: 15px; border-bottom: 1px solid rgba(255, 255, 255, 0.05);">
                              <h3 style="background: linear-gradient(114deg, #17c964 0%, #0170ef 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 10px; font-size: 1.5rem; font-weight: 600;">Available Investment Plans</h3>
                              <p style="color: #848e9c; margin: 0; font-size: 14px;">Select the investment plan that best suits your financial goals.</p>
                            </div>

                            <!-- Investment Plans -->
                            <div class="investment-plans-container">
                              <div class="row">
                                <?php while ($row = mysqli_fetch_object($result)) {
                                  $i++;
                                  $j++;
                                  if ($j == 5) {
                                    $j = 1;
                                  }
                                ?>
                                  <div class="col-xs-12 col-sm-6 col-lg-4">
                                    <div class="investment-plan-card">
                                      <div class="plan-header">
                                        <div class="plan-title"><?php echo $row->title; ?></div>
                                        <div class="plan-amount">
                                          <?php
                                          echo ($row->recid == '1') ? "First" : (($row->recid == '2') ? "Second" : "Third");
                                          ?>
                                          Package: </br>
                                          <?php
                                          echo ($row->amount_from == $row->amount_to)
                                            ? '$' . number_format($row->amount_from * 1)
                                            : '$' . number_format($row->amount_from * 1) . ' - $' . number_format($row->amount_to * 1);
                                          ?>
                                        </div>
                                      </div>

                                      <ul class="plan-features">
                                        <?php if ($row->line1) { ?>
                                          <li><?php echo $row->line1; ?></li>
                                        <?php } ?>
                                        <?php if ($row->line2) { ?>
                                          <li><?php echo $row->line2; ?></li>
                                        <?php } ?>
                                        <li>Secure Investment Platform</li>
                                        <li>24/7 Customer Support</li>
                                      </ul>
                                           <?php
                                            $canInvest = ($row->recid == ($user->package + 1));
                                            $disabled = $canInvest ? '' : 'disabled';
                                            ?>
                                            <button class="invest-btn" onclick="window.location.href='invest_now.php?i=<?php echo $row->recid; ?>'" <?php echo $disabled; ?>>
                                                <i class="fas fa-chart-line"></i> 
                                                <?php echo $canInvest ? 'INVEST NOW' : 'Locked'; ?>
                                            </button>
                                    </div>
                                  </div>
                                <?php } ?>
                              </div>
                            </div>







                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  </div>
  </div>

  <!-- Essential Libraries with CDN fallbacks -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-countup/1.0.0/jquery.countup.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/waypoints/4.0.1/jquery.waypoints.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

  <!-- Try to load local assets, but don't fail if they're missing -->
  <script>
    // Load local assets if available, but don't break if they're missing
    function loadScript(src) {
      var script = document.createElement('script');
      script.src = src;
      script.onerror = function() {
        console.log('Optional script failed to load:', src);
      };
      document.head.appendChild(script);
    }

    // Optional local scripts
    loadScript('./assets/cy/js/common.js?vs=100');
    loadScript('./sidebar.js');
  </script>

  <!-- Main Application Script -->
  <script>
    // Wait for jQuery to be available
    (function() {
      function initializeApp() {
        if (typeof jQuery === 'undefined') {
          console.log('jQuery not available, retrying...');
          setTimeout(initializeApp, 100);
          return;
        }

        console.log('jQuery loaded successfully, version:', $.fn.jquery);

        /* Loop through all dropdown buttons to toggle between hiding and showing its dropdown content */
        var dropdown = document.getElementsByClassName("dropdown-btn");
        var i;

        for (i = 0; i < dropdown.length; i++) {
          dropdown[i].addEventListener("click", function() {
            this.classList.toggle("active");
            this.classList.toggle("dactive");
            var dropdownContent = this.nextElementSibling;
            if (dropdownContent.style.display === "block") {
              dropdownContent.style.display = "none";
            } else {
              dropdownContent.style.display = "block";
            }
          });
        }

        $(document).ready(function() {
          console.log('Document ready - Investment page initialized');
        });
      }

      // Initialize the app
      initializeApp();
    })();
  </script>

  <script>
    function show() {
      document.querySelector(".mobile-panel-block").setAttribute("class", "mobile-panel-block active")
    }

    function closeSidebar() {
      document.querySelector(".mobile-panel-block").setAttribute("class", "mobile-panel-block")
    }
  </script>

</body>

</html>