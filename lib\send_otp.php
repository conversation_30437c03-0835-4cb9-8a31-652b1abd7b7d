<?php
session_start();
header('Content-Type: application/json');

require_once 'config.php'; // Make sure this includes your database connection

$email = $_POST['email'] ?? '';
$response = ['success' => false, 'message' => ''];

if(!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    $response['message'] = 'Invalid email format';
    echo json_encode($response);
    exit;
}

// Check if email already exists in database
// This assumes you have a function to check email existence
if(emailExists($email)) {
    $response['message'] = 'Email already registered';
    echo json_encode($response);
    exit;
}

// Generate 6-digit OTP
$otp = rand(100000, 999999);
$_SESSION['email_otp'] = $otp;
$_SESSION['email_otp_email'] = $email;
$_SESSION['email_otp_time'] = time();

// For now, we'll just return success
// We'll implement the actual email sending in the next step
$response['success'] = true;
$response['message'] = 'OTP sent successfully';

echo json_encode($response);

function emailExists($email) {
    // Implement your email existence check logic here
    // Return true if email exists, false otherwise
    return false;
}
?>